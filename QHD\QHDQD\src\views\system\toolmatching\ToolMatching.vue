<!--
 *代码由框架生成,任何更改都可能导致被代码生成器覆盖
 *业务请在@/extension/system/toolmatching/ToolMatching.js此处编写
 -->
<template>
    <view-grid ref="grid"
               :columns="columns"
               :detail="detail"
               :editFormFields="editFormFields"
               :editFormOptions="editFormOptions"
               :searchFormFields="searchFormFields"
               :searchFormOptions="searchFormOptions"
               :table="table"
               :extend="extend">
    </view-grid>
</template>
<script>
    import extend from "@/extension/system/toolmatching/ToolMatching.js";
    import { ref, defineComponent } from "vue";
    export default defineComponent({
        setup() {
            const table = ref({
                key: 'ID',
                footer: "Foots",
                cnName: '刀具配对',
                name: 'toolmatching/ToolMatching',
                url: "/ToolMatching/",
                sortName: "ID"
            });
            const editFormFields = ref({"WarehouseNumber":"","NaturePosition":"","PositionType":"","KnifeTypeNumber":"","ToolNumber":"","ToolClass":"","ToolSpecification":"","ToolModel":"","KnifeHandleSpecifications":"","ClampingLength":"","KnifeHeight":"","ProcessedMaterial":"","Functionality":"","H":"","D":"","MonitoringMethod":"","ReplacementMethod":"","SetValue":"","ActualData":"","KnifeRequirements":"","ToolCondition":"","CurrentTask":""});
            const editFormOptions = ref([[{"title":"仓号 ","field":"WarehouseNumber"},
                               {"dataKey":"刀具仓位限制","data":[],"title":"仓位性质","field":"NaturePosition","type":"select"},
                               {"dataKey":"刀具仓位类型","data":[],"title":"仓位类型","field":"PositionType","type":"select"},
                               {"title":"刀型编号","field":"KnifeTypeNumber"}],
                              [{"title":"刀具编号","field":"ToolNumber"},
                               {"dataKey":"刀具类别","data":[],"title":"刀具类别","field":"ToolClass","type":"select"},
                               {"title":"刀具规格","field":"ToolSpecification"},
                               {"title":"刀具型号","field":"ToolModel"}],
                              [{"title":"刀柄规格","field":"KnifeHandleSpecifications"},
                               {"title":"装夹长度","field":"ClampingLength"},
                               {"title":"对刀高度","field":"KnifeHeight"},
                               {"dataKey":"刀具加工材质","data":[],"title":"加工材质","field":"ProcessedMaterial","type":"select"}],
                              [{"dataKey":"刀具使用功能","data":[],"title":"使用功能","field":"Functionality","type":"select"},
                               {"title":"H","field":"H"},
                               {"title":"D","field":"D"},
                               {"dataKey":"刀具监控方式","data":[],"title":"监控方式","field":"MonitoringMethod","type":"select"}],
                              [{"dataKey":"刀具更换方式","data":[],"title":"更换方式","field":"ReplacementMethod","type":"select"},
                               {"title":"设定值","field":"SetValue"},
                               {"title":"实际数据","field":"ActualData"},
                               {"dataKey":"刀具对刀要求","data":[],"title":"对刀要求","field":"KnifeRequirements","type":"select"}],
                              [{"dataKey":"刀具状态","data":[],"title":"刀具状态","field":"ToolCondition","type":"select"},
                               {"title":"当前任务","field":"CurrentTask"}]]);
            const searchFormFields = ref({"PositionType":""});
            const searchFormOptions = ref([[{"dataKey":"刀具仓位类型","data":[],"title":"仓位类型","field":"PositionType","type":"select"}]]);
            const columns = ref([{field:'ID',title:'ID',type:'int',link:true,width:110,hidden:true,readonly:true,require:true,align:'left'},
                       {field:'WarehouseNumber',title:'仓号 ',type:'string',width:110,align:'left',sort:true},
                       {field:'NaturePosition',title:'仓位性质',type:'string',bind:{ key:'刀具仓位限制',data:[]},width:110,align:'left'},
                       {field:'PositionType',title:'仓位类型',type:'string',bind:{ key:'刀具仓位类型',data:[]},width:110,align:'left'},
                       {field:'KnifeTypeNumber',title:'刀型编号',type:'string',width:110,align:'left'},
                       {field:'ToolNumber',title:'刀具编号',type:'string',width:110,align:'left'},
                       {field:'ToolClass',title:'刀具类别',type:'string',bind:{ key:'刀具类别',data:[]},width:110,align:'left'},
                       {field:'ToolSpecification',title:'刀具规格',type:'string',width:110,align:'left'},
                       {field:'ToolModel',title:'刀具型号',type:'string',width:110,align:'left'},
                       {field:'KnifeHandleSpecifications',title:'刀柄规格',type:'string',width:110,align:'left'},
                       {field:'ClampingLength',title:'装夹长度',type:'string',width:110,align:'left'},
                       {field:'KnifeHeight',title:'对刀高度',type:'string',width:110,align:'left'},
                       {field:'ProcessedMaterial',title:'加工材质',type:'string',bind:{ key:'刀具加工材质',data:[]},width:110,align:'left'},
                       {field:'Functionality',title:'使用功能',type:'string',bind:{ key:'刀具使用功能',data:[]},width:110,align:'left'},
                       {field:'H',title:'H',type:'string',width:110,align:'left'},
                       {field:'D',title:'D',type:'string',width:110,align:'left'},
                       {field:'MonitoringMethod',title:'监控方式',type:'string',bind:{ key:'刀具监控方式',data:[]},width:110,align:'left'},
                       {field:'ReplacementMethod',title:'更换方式',type:'string',bind:{ key:'刀具更换方式',data:[]},width:110,align:'left'},
                       {field:'SetValue',title:'设定值',type:'string',width:110,align:'left'},
                       {field:'ActualData',title:'实际数据',type:'string',width:110,align:'left'},
                       {field:'KnifeRequirements',title:'对刀要求',type:'string',bind:{ key:'刀具对刀要求',data:[]},width:110,align:'left'},
                       {field:'ToolCondition',title:'刀具状态',type:'string',bind:{ key:'刀具状态',data:[]},width:110,align:'left'},
                       {field:'CurrentTask',title:'当前任务',type:'string',width:110,align:'left'}]);
            const detail = ref({
                cnName: "#detailCnName",
                table: "#detailTable",
                columns: [],
                sortName: "",
                key: ""
            });
            return {
                table,
                extend,
                editFormFields,
                editFormOptions,
                searchFormFields,
                searchFormOptions,
                columns,
                detail,
            };
        },
    });
</script>
