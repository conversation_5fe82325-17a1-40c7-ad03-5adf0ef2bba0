<template>
	<div>
		<h3>
			<i class="ivu-icon ivu-icon-ios-information-circle-outline"></i>
			<div class="desc-text">
				<i class="el-icon-s-grid"></i>
				<span>组装单据明细</span>
			</div>
		</h3>
		<!-- url="/api/SellOrder/getDetailPage"-->
		<div style="padding: 10px; background: white; padding-top: 0">
			<vol-table ref="tableList" :loadKey="true" :columns="columns" :pagination-hide="true" :height="420"
				:defaultLoadPage="false" @loadBefore="loadBefore" url="api/WMS_Assembly/getDetailPage"
				:index="false" :ck="false"></vol-table>
		</div>
		<vol-box :lazy="true" v-model="detailModel" title="对刀仪数据" :width="700" :padding="5">
			 <div style="padding: 10px;"><vol-form ref="form" :editor="editor" :load-key="false" :label-width="'120px'" :formRules="editFormOptions"
				:formFields="editFormFields" :select2Count="2000"></vol-form></div>
		</vol-box>
		<vol-box :lazy="true" v-model="qrCodeModel" title="二维码" :width="300" :padding="15">
			<div style="text-align: center;">
				<img :src="qrCodeImage" alt="QR Code" style="width: 200px; height: 200px;"/>
				<p style="margin-top: 10px;">组刀信息二维码</p>
			</div>
		</vol-box>
	</div>
</template>
<script>
import VolTable from "@/components/basic/VolTable.vue";
import VolForm from '@/components/basic/VolForm.vue';
import VolBox from '@/components/basic/VolBox.vue';
import QRCode from 'qrcode';
export default {
	components: {
		VolTable,
		VolForm,
		VolBox
	},
	methods: {
		loadBefore(params, callback) {
			return callback(true);
		},
		showQRCode(qrData) {
			// 生成二维码并显示在弹窗中
			QRCode.toCanvas(qrData, { width: 200 }, (error, canvas) => {
				if (error) {
					console.error(error);
					this.$message.error('生成二维码失败');
					return;
				}
				
				// 将canvas转换为图片并显示在弹窗中
				this.qrCodeImage = canvas.toDataURL('image/png');
				this.qrCodeModel = true;
			});
		}
	},
	data() {
		return {
			detailModel: false,
			qrCodeModel: false, // 二维码弹窗控制
			qrCodeImage: '', // 二维码图片数据
			editFormFields: { },
			editFormOptions: [[{ "title": "物品名称", "field": "Name" },
			{ "title": "物料编码", "field": "MaterialNumber" }],
			[{ "title": "转换套", "field": "ConvertCover" },
			{ "title": "日期", "field": "Date" }],
			[{ "title": "刀具标识号", "field": "KnifeNumber" },
			{ "title": "名称", "field": "Name"}],
			[{ "title": "X", "field": "X" },
			{ "title": "Z", "field": "Z" }],
			[{ "title": "R",  "field": "R" },
			{ "title": "A1", "field": "A1" }],
			[{ "title": "A2", "field": "A2" }]
			],
			editor: {
				uploadImgUrl: '', //上传路径
				upload: null //上传方法
			},
			tableData: [],
			columns: [{ field: 'id', title: 'id', type: 'int', width: 110, hidden: true, readonly: true, require: true, align: 'left' },
			{ field: 'AssemblyID', title: '组装主表id', type: 'int', width: 110, hidden: true, align: 'left' },
			{ field: 'ItemID', title: '物料id', type: 'int', width: 110, hidden: true, align: 'left' },
			{ field: 'ItemType', title: '物料类别', type: 'int', bind: { key: '物料类型', data: [] }, width: 110, align: 'left', sort: true },
			{ field: 'ItemName', title: '物料名称', type: 'string', width: 220, align: 'left' },
			{ field: 'ItemCode', title: '物料编号', type: 'string', width: 220, align: 'left' },
			{field:'ToolNumber',title:'刀号',type:'string',width:100,align:'left'},
			{field:'ContainerPoint',title:'放置托盘位置',type:'int',width:120,edit:{type:''},align:'left'},
			{ field: 'ItemModel', title: '刀型编号', type: 'string', width: 220, align: 'left' },
			{ field: 'ToolHandleModel', title: '刀柄规格', type: 'string', bind: { key: '刀柄规格', data: [] }, width: 110, align: 'left', sort: true },
			{ field: 'BindiToolHandle', title: '刀柄绑定二维码', type: 'string', width: 180, align: 'left' },
			{field:'Status',title:'是否有对刀仪数据',type:'int',width: 120,bind:{ key:'enable',data:[]},align:'left'},
			{
				title: '操作',
				hidden: false,
				align: "center",
				fixed: 'right',
				width: 220,
				render: (h, { row, column, index }) => {
					if (row.Status == 0) {
						return h(
							"div", { style: { 'font-size': '13px', 'cursor': 'pointer', 'color': '#409eff' } }, [
							h(
								"a", {
								style: { 'margin-right': '15px' },
								onClick: (e) => {
									e.stopPropagation()  
									this.http.post('/api/WMS_Assembly/CheckTool', {mnumber:row.ItemCode}, true).then((x)=>{
										if(x.status){
											this.$message.success('绑定成功');

										}else{
											this.$message.error(x.message);
										}
									})  
								}
							},
							 "检测"
							),
							h(
								"a", {
								style: { 'margin-right': '15px' },
								onClick: (e) => {
									e.stopPropagation()                                    
								}
							}, "解绑"
							),
							h(
								"a", {
								style: { 'margin-right': '15px' },
								onClick: (e) => {
									e.stopPropagation()   
                       
                                     this.http.get('/api/ToolPresetter/SelectToolPresetter?id='+row.ItemCode,
									 {

									 },true).then((x) => {
                                                this.editFormFields = {
													Name: x.data.name,
													MaterialNumber:x.data.materialNumber,
													ConvertCover:x.data.convertCover,
													Date:x.data.date,
													KnifeNumber:x.data.knifeNumber,
													X:x.data.x,
													R:x.data.r,
													A1:x.data.a1,
													A2:x.data.a2,
													Z:x.data.z
												}
                                      });

									this.detailModel = true
								}
							}, "对刀仪记录"
							),
							h(
								"a", {
								style: { 'margin-right': '15px' },
								onClick: (e) => {
									e.stopPropagation();
									// 生成包含行数据的二维码，格式为：{放置托盘位置/物料规格型号/刀柄绑定二维码}
									const qrData = `{${row.ContainerPoint || ''}/${row.ToolHandleModel || ''}/${row.ItemModel || ''}/${row.ToolNumber || ''}}`;
									this.showQRCode(qrData);
								}
							}, "二维码"
							)
						])
					}else{
						return h(
							"div", { style: { 'font-size': '13px', 'cursor': 'pointer', 'color': '#409eff' } }, [
							h(
								"a", {
								style: { 'margin-right': '15px' },
								onClick: (e) => {
									e.stopPropagation()   
                       
                                     this.http.get('/api/ToolPresetter/ToolPresetterUnbind?BindiToolHandle=' + row.BindiToolHandle + '&id=' + row.Assemblyid,
									 {

									 },true).then((x) => {
                                              
                                      });

									this.detailModel = true
								}
							}, "解绑"
							),
							h(
								"a", {
								style: { 'margin-right': '15px' },
								onClick: (e) => {
									e.stopPropagation()   
                       
                                     this.http.get('/api/ToolPresetter/SelectToolPresetter?id='+row.ItemCode,
									 {

									 },true).then((x) => {
                                                this.editFormFields = {
													Name: x.data.name,
													MaterialNumber:x.data.materialNumber,
													ConvertCover:x.data.convertCover,
													Date:x.data.date,
													KnifeNumber:x.data.knifeNumber,
													X:x.data.x,
													R:x.data.r,
													A1:x.data.a1,
													A2:x.data.a2,
													Z:x.data.z
												}
                                      });

									this.detailModel = true
								}
							}, "对刀仪记录"
							),
							h(
								"a", {
								style: { 'margin-right': '15px' },
								onClick: (e) => {
									e.stopPropagation();
									// 生成包含行数据的二维码，格式为：{放置托盘位置/物料规格型号/刀柄绑定二维码}
									const qrData = `{${row.ContainerPoint || ''}/${row.ItemModel || ''}/${row.BindiToolHandle || ''}}`;
									this.showQRCode(qrData);
								}
							}, "二维码"
							)
						])

					}
				}
			}
			]
		}
	}
}
</script>
<style scoped>
h3 {
	font-weight: 500;
	padding-left: 10px;
	background: white;
	margin-top: 8px;
	padding-bottom: 5px;
}
</style>